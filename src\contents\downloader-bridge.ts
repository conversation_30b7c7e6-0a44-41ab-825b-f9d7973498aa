import {
  BackgroundMessageType,
  PageToExtensionMessageType,
  ExtensionToPageMessageType
} from "../types/network"
import type { Response } from "../types/network"
import type { PageMessage } from "../types"

// ==================== 配置和常量 ====================

export const config = {
  matches: [
    // "http://localhost:3000/*",
    "http://localhost:3000/downloader*",
    "https://localhost:3000/downloader*",
    "http://localhost:3000/*/downloader*",
    "https://localhost:3000/*/downloader*",
    // "http://localhost:3456/*",
    "http://snapany.com/downloader*",
    "https://snapany.com/downloader*",
    "http://snapany.com/*/downloader*",
    "https://snapany.com/*/downloader*",
    // "https://snapany.com/*"

  ]
}

/** 支持的来自background的消息类型 */
const SUPPORTED_BACKGROUND_MESSAGE_TYPES = [
  ExtensionToPageMessageType.HEADERS_SET_COMPLETED,
  ExtensionToPageMessageType.DOWNLOAD_DATA_RESPONSE,
  ExtensionToPageMessageType.DOWNLOAD_FILE_RESPONSE
] as const

// ==================== 工具函数 ====================

/**
 * 发送消息到background脚本
 * @param type - 消息类型
 * @param payload - 消息载荷数据
 * @returns Promise<Response> 返回background脚本的响应
 */
async function sendMessageToBackground(type: BackgroundMessageType | PageToExtensionMessageType, payload?: any): Promise<Response> {
  return await chrome.runtime.sendMessage({
    type,
    payload
  })
}

/**
 * 通过background脚本发送消息到标签页
 * @param messageType - 消息类型
 * @param data - 消息数据
 * @param pageTaskId - 页面任务ID，用于消息过滤
 */
async function sendMessageToTab(messageType: string, data: any, pageTaskId?: string): Promise<void> {
  await sendMessageToBackground(BackgroundMessageType.SEND_TO_TAB, {
    type: messageType,
    data,
    pageTaskId
  })
}

// ==================== 消息处理器 ====================

/**
 * 消息处理器类
 * 包含所有页面消息的处理方法
 */
class MessageHandlers {
  /**
   * 处理获取下载数据请求
   * @param data - 包含requestId的请求数据
   */
  static async handleGetDownloadDataById(data: any): Promise<void> {
    try {
      console.log('处理获取下载数据请求，收到的data:', data);

      // 从消息数据中提取requestId
      const requestId = data.data?.requestId || data.requestId;
      console.log('提取到的requestId:', requestId);

      if (!requestId) {
        throw new Error('缺少请求ID参数');
      }

      const response = await sendMessageToBackground(PageToExtensionMessageType.GET_DOWNLOAD_DATA_BY_ID, { requestId })

      window.postMessage({
        type: ExtensionToPageMessageType.DOWNLOAD_DATA_RESPONSE,
        data: response
      }, window.location.origin)
    } catch (error) {
      console.error('获取下载数据失败:', error)

      window.postMessage({
        type: ExtensionToPageMessageType.DOWNLOAD_DATA_RESPONSE,
        data: {
          success: false,
          error: error instanceof Error ? error.message : '获取下载数据失败'
        }
      }, window.location.origin)
    }
  }



  /**
   * 处理下载文件请求（仅用于设置请求头）
   * @param data - 包含下载信息和pageTaskId的数据
   * @description 此函数仅用于设置请求头，实际下载由前端处理
   */
  static async handleDownloadFileWithHeaders(data: any): Promise<void> {
    try {
      console.log("处理设置请求头请求:", data)

      // 从消息数据中提取实际的数据
      const actualData = data.data || data;
      console.log("提取到的实际数据:", actualData);

      // 此函数仅用于设置请求头，实际下载由前端处理
      const response = await sendMessageToBackground(PageToExtensionMessageType.DOWNLOAD_FILE_WITH_HEADERS, actualData)

      await sendMessageToTab(ExtensionToPageMessageType.DOWNLOAD_FILE_RESPONSE, response, actualData.pageTaskId)
    } catch (error) {
      console.error('设置请求头失败:', error)
      await sendMessageToTab(
        ExtensionToPageMessageType.DOWNLOAD_FILE_RESPONSE,
        {
          success: false,
          error: error instanceof Error ? error.message : '设置请求头失败'
        },
        data.pageTaskId || data.data?.pageTaskId
      )
    }
  }

  /**
   * 处理页面的ping消息，回应pong表示插件存在且正常工作
   */
  static async handleExtensionPing(data: any): Promise<void> {
    console.log('收到页面ping消息，回应pong', data)

    // 立即回应pong消息
    window.postMessage({
      type: ExtensionToPageMessageType.EXTENSION_PONG,
      timestamp: Date.now(),
      pageTaskId: data.pageTaskId,
      pingTimestamp: data.timestamp
    }, window.location.origin)
  }
}

const PAGE_MESSAGE_HANDLERS = {
  [PageToExtensionMessageType.GET_DOWNLOAD_DATA_BY_ID]: MessageHandlers.handleGetDownloadDataById,
  [PageToExtensionMessageType.DOWNLOAD_FILE_WITH_HEADERS]: MessageHandlers.handleDownloadFileWithHeaders,
  [PageToExtensionMessageType.EXTENSION_PING]: MessageHandlers.handleExtensionPing,
} as const

// ==================== 事件监听和初始化 ====================

console.log("content script 已加载至此界面，此消息由插件打印")

/**
 * 初始化下载器桥梁
 * 通知页面内容脚本已准备就绪
 */
function initializeBridge(): void {
  console.log("初始化下载器桥梁")

  window.postMessage({
    type: ExtensionToPageMessageType.CONTENT_SCRIPT_READY,
    data: {}
  }, window.location.origin)
}

/**
 * 处理来自background脚本的消息
 * @param message - 来自background的消息对象
 * @param _sender - 消息发送者信息（未使用）
 * @param sendResponse - 响应回调函数
 * @returns boolean 是否处理了该消息
 */
function handleBackgroundMessage(message: any, _sender: any, sendResponse: (response: any) => void): boolean {
  console.log("内容脚本收到来自background的消息:", message)

  if (SUPPORTED_BACKGROUND_MESSAGE_TYPES.includes(message.type)) {
    console.log(`准备转发消息 ${message.type} 到页面，消息数据:`, message.data)

    // 转发消息到页面，页面会根据pageTaskId进行过滤
    window.postMessage({
      type: message.type,
      data: message.data
    }, window.location.origin)

    sendResponse({ success: true })
    return true
  }

  console.log(`不支持的消息类型: ${message.type}`)
  return false
}

/**
 * 处理来自页面的消息
 * @param event - 页面消息事件对象
 */
async function handlePageMessage(event: MessageEvent<PageMessage>): Promise<void> {
  if (event.source !== window) return

  const { type, ...data } = event.data
  console.log("内容脚本收到消息:", type, data)

  // 只处理页面发给插件的消息类型，忽略插件发给页面的响应消息
  // 这些响应消息是内容脚本发给页面的，不应该被内容脚本自己的监听器处理
  if (Object.values(ExtensionToPageMessageType).includes(type as ExtensionToPageMessageType)) {
    console.log("忽略插件发给页面的响应消息:", type)
    return
  }

  try {
    const handler = PAGE_MESSAGE_HANDLERS[type as keyof typeof PAGE_MESSAGE_HANDLERS]
    if (handler) {
      await handler(data)
    } else {
      console.log("忽略未知消息类型:", type)
    }
  } catch (error) {
    console.error("处理消息时发生错误:", error)
  }
}

// ==================== 事件监听器注册 ====================

chrome.runtime.onMessage.addListener(handleBackgroundMessage)
window.addEventListener('message', handlePageMessage)

if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeBridge)
} else {
  initializeBridge()
}