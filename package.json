{"name": "snapany-extension", "displayName": "__MSG_extensionName__", "version": "1.0.0", "description": "__MSG_extensionDescription__", "author": "Plasmo Corp. <<EMAIL>>", "scripts": {"dev": "plasmo dev", "build": "plasmo build", "package": "plasmo package"}, "dependencies": {"@tabler/icons-react": "^3.34.1", "@types/hls.js": "^1.0.0", "flowbite-react": "^0.12.5", "flowbite-react-icons": "^1.3.0", "hls.js": "^1.6.5", "i18next": "^25.3.2", "plasmo": "0.90.5", "react": "18.2.0", "react-dom": "18.2.0", "react-i18next": "^15.6.1"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "4.1.1", "@types/chrome": "0.0.258", "@types/node": "20.11.5", "@types/react": "18.2.48", "@types/react-dom": "18.2.18", "autoprefixer": "^10.4.21", "flowbite": "^3.1.2", "postcss": "^8.5.4", "prettier": "3.2.4", "tailwindcss": "^3.4.17", "typescript": "5.3.3"}, "manifest": {"default_locale": "en", "host_permissions": ["https://*/*", "http://*/*"], "permissions": ["webRequest", "tabs", "activeTab", "storage", "declarativeNetRequest", "sidePanel", "scripting"], "side_panel": {"default_path": "sidepanel.html"}}}